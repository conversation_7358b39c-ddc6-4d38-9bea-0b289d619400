from unittest.mock import Mock

import pytest

from app.integrations.adapters.hubspot.client import HubSpotClient, HubSpotClientError


@pytest.fixture
def hubspot_client():
    return HubSpotClient(access_token="test_access_token")


@pytest.fixture
def mock_hubspot_api_client(mocker):
    """Mock the HubSpot API client"""
    mock_client = Mock()
    mocker.patch(
        "app.integrations.adapters.hubspot.client.HubSpot", return_value=mock_client
    )
    return mock_client


class TestHubSpotClient:
    def test_init_with_access_token(self):
        client = HubSpotClient(access_token="test_token")
        assert client.access_token == "test_token"
        assert client._client is None

    def test_init_without_access_token(self):
        client = HubSpotClient()
        assert client.access_token is None
        assert client._client is None

    def test_create_client_with_access_token(
        self, hubspot_client, mock_hubspot_api_client
    ):
        client = hubspot_client._create_client()
        assert client is not None

    def test_create_client_without_access_token(self):
        client = HubSpotClient()
        with pytest.raises(HubSpotClientError, match="Failed to create HubSpot client"):
            client._create_client()

    def test_client_property_creates_client_once(
        self, hubspot_client, mock_hubspot_api_client
    ):
        # First access should create the client
        client1 = hubspot_client.client
        # Second access should return the same client
        client2 = hubspot_client.client
        assert client1 is client2

    @pytest.mark.anyio
    async def test_get_user_info_success(self, hubspot_client, mock_hubspot_api_client):
        # Mock the API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "user_id": 12345,
            "hub_id": 67890,
            "app_id": 111111,
            "expires_in": 1754,
        }
        mock_hubspot_api_client.api_request.return_value = mock_response

        result = await hubspot_client.get_user_info()

        assert result == {
            "user_id": 12345,
            "hub_id": 67890,
            "app_id": 111111,
            "expires_in": 1754,
        }

        # Verify the correct endpoint was called
        mock_hubspot_api_client.api_request.assert_called_once_with(
            {"path": "/oauth/v1/access-tokens/test_access_token"}
        )

    @pytest.mark.anyio
    async def test_get_user_info_api_error(
        self, hubspot_client, mock_hubspot_api_client
    ):
        # Mock an API error
        mock_hubspot_api_client.api_request.side_effect = Exception("API Error")

        with pytest.raises(HubSpotClientError, match="Failed to get user info"):
            await hubspot_client.get_user_info()

    @pytest.mark.anyio
    async def test_get_user_info_without_access_token(self):
        client = HubSpotClient()

        with pytest.raises(HubSpotClientError, match="Failed to get user info"):
            await client.get_user_info()
